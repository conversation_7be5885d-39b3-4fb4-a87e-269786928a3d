#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
游戏存档解密工具
用于破解HYYM游戏的加密存档文件
"""

import base64
import re
import sys
from typing import Optional

def analyze_encryption_pattern(encrypted_data: str, decrypted_data: str) -> None:
    """分析加密模式"""
    print("=== 加密模式分析 ===")
    print(f"加密数据长度: {len(encrypted_data)}")
    print(f"解密数据长度: {len(decrypted_data)}")
    print(f"加密数据前50字符: {encrypted_data[:50]}")
    print(f"解密数据前50字符: {decrypted_data[:50]}")
    
    # 检查是否是简单的字符替换
    unique_chars_encrypted = set(encrypted_data)
    unique_chars_decrypted = set(decrypted_data)
    print(f"加密数据唯一字符数: {len(unique_chars_encrypted)}")
    print(f"解密数据唯一字符数: {len(unique_chars_decrypted)}")

def try_base64_decode(data: str) -> Optional[str]:
    """尝试Base64解码"""
    try:
        # 移除可能的填充字符
        cleaned_data = data.replace('#', '=').replace('$', '/')
        decoded = base64.b64decode(cleaned_data + '==')

        # 尝试不同的编码
        encodings = ['utf-8', 'gbk', 'gb2312', 'latin1']
        for encoding in encodings:
            try:
                result = decoded.decode(encoding)
                print(f"使用编码 {encoding} 解码成功")
                return result
            except UnicodeDecodeError:
                continue

        # 如果都失败了，返回原始字节的十六进制表示
        print("所有编码都失败，返回十六进制表示")
        return decoded.hex()

    except Exception as e:
        print(f"Base64解码失败: {e}")
        return None

def try_character_mapping(encrypted_data: str, decrypted_data: str) -> dict:
    """尝试建立字符映射表"""
    mapping = {}
    
    # 找到对应的字符
    min_len = min(len(encrypted_data), len(decrypted_data))
    
    for i in range(min_len):
        enc_char = encrypted_data[i]
        dec_char = decrypted_data[i]
        
        if enc_char in mapping:
            if mapping[enc_char] != dec_char:
                print(f"字符映射冲突: {enc_char} -> {mapping[enc_char]} vs {dec_char}")
        else:
            mapping[enc_char] = dec_char
    
    return mapping

def apply_character_mapping(data: str, mapping: dict) -> str:
    """应用字符映射"""
    result = ""
    for char in data:
        if char in mapping:
            result += mapping[char]
        else:
            result += char  # 保持原字符
    return result

def extract_encrypted_content(file_content: str) -> Optional[str]:
    """从文件内容中提取加密部分"""
    # 查找@符号后的内容
    match = re.search(r'@([^<]+)', file_content)
    if match:
        return match.group(1)
    return None

def extract_decrypted_content(file_content: str) -> Optional[str]:
    """从解密文件中提取内容部分"""
    # 对于已解密的文件，直接提取@符号后的所有内容
    at_pos = file_content.find('@')
    if at_pos != -1:
        return file_content[at_pos + 1:]

    # 如果没有@符号，可能整个文件就是解密内容
    # 查找第一个<符号开始的XML内容
    xml_start = file_content.find('<', file_content.find('>') + 1)  # 跳过第一个标签
    if xml_start != -1:
        return file_content[xml_start:]

    return None

def decrypt_save_file(encrypted_file_path: str, reference_file_path: str, output_file_path: str) -> bool:
    """解密存档文件"""
    try:
        # 读取加密文件
        with open(encrypted_file_path, 'r', encoding='utf-8') as f:
            encrypted_content = f.read()
        
        # 读取参考解密文件
        with open(reference_file_path, 'r', encoding='utf-8') as f:
            reference_content = f.read()
        
        # 提取文件头部（@符号之前的部分）
        header_match = re.match(r'([^@]+@)', encrypted_content)
        if not header_match:
            print("无法找到文件头部")
            return False
        
        header = header_match.group(1)
        
        # 提取加密数据
        encrypted_data = extract_encrypted_content(encrypted_content)
        if not encrypted_data:
            print("无法提取加密数据")
            return False
        
        # 提取参考文件的解密数据
        reference_data = extract_decrypted_content(reference_content)
        if not reference_data:
            print("无法提取参考解密数据")
            return False
        
        print("=== 开始解密分析 ===")
        analyze_encryption_pattern(encrypted_data, reference_data)
        
        # 尝试不同的解密方法
        
        # 方法1: 尝试Base64解码
        print("\n=== 尝试Base64解码 ===")
        base64_result = try_base64_decode(encrypted_data)
        if base64_result:
            print("Base64解码成功!")
            decrypted_content = header + base64_result
        else:
            # 方法2: 尝试字符映射
            print("\n=== 尝试字符映射 ===")
            char_mapping = try_character_mapping(encrypted_data, reference_data)
            print(f"建立了 {len(char_mapping)} 个字符映射")
            
            # 显示部分映射
            sample_mappings = list(char_mapping.items())[:20]
            print("示例映射:", sample_mappings)
            
            # 应用映射
            mapped_result = apply_character_mapping(encrypted_data, char_mapping)
            decrypted_content = header + mapped_result
        
        # 保存解密结果
        with open(output_file_path, 'w', encoding='utf-8') as f:
            f.write(decrypted_content)
        
        print(f"\n解密完成! 结果已保存到: {output_file_path}")
        return True
        
    except Exception as e:
        print(f"解密过程中出现错误: {e}")
        return False

def main():
    """主函数"""
    encrypted_file = "gamedata/modcache/HYYM/saves/save0"
    reference_file = "gamedata/modcache/HYYM/saves/autosave.txt"
    output_file = "gamedata/modcache/HYYM/saves/save0_decrypted.txt"
    
    print("HYYM游戏存档解密工具")
    print("=" * 50)
    
    success = decrypt_save_file(encrypted_file, reference_file, output_file)
    
    if success:
        print("解密成功!")
    else:
        print("解密失败!")

if __name__ == "__main__":
    main()
