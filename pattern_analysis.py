#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模式分析工具 - 分析加密数据的模式
"""

import re
from collections import Counter

def analyze_patterns():
    """分析加密数据的模式"""
    # 读取加密文件
    with open("gamedata/modcache/HYYM/saves/save0", 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 提取加密数据
    at_pos = content.find('@')
    encrypted_data = content[at_pos + 1:]
    
    print("=== 模式分析 ===")
    print(f"数据长度: {len(encrypted_data)}")
    print(f"前200字符: {encrypted_data[:200]}")
    
    # 分析特殊字符的位置
    special_chars = ['#', '$', '<', '>']
    for char in special_chars:
        positions = [i for i, c in enumerate(encrypted_data) if c == char]
        print(f"\n字符 '{char}' 出现 {len(positions)} 次")
        if len(positions) <= 20:
            print(f"  位置: {positions}")
        else:
            print(f"  前10个位置: {positions[:10]}")
            print(f"  后10个位置: {positions[-10:]}")
    
    # 分析<>对的模式
    print("\n=== 分析<>对模式 ===")
    lt_positions = [i for i, c in enumerate(encrypted_data) if c == '<']
    gt_positions = [i for i, c in enumerate(encrypted_data) if c == '>']
    
    print(f"< 出现 {len(lt_positions)} 次")
    print(f"> 出现 {len(gt_positions)} 次")
    
    # 查找<>对
    pairs = []
    for lt_pos in lt_positions:
        # 找到最近的>
        for gt_pos in gt_positions:
            if gt_pos > lt_pos:
                pairs.append((lt_pos, gt_pos, encrypted_data[lt_pos:gt_pos+1]))
                break
    
    print(f"找到 {len(pairs)} 个<>对:")
    for i, (start, end, content) in enumerate(pairs[:10]):
        print(f"  {i+1}: 位置{start}-{end}, 内容: {content}")
    
    # 分析#$的模式
    print("\n=== 分析#$模式 ===")
    hash_positions = [i for i, c in enumerate(encrypted_data) if c == '#']
    dollar_positions = [i for i, c in enumerate(encrypted_data) if c == '$']
    
    # 查看#和$周围的字符
    print("# 字符周围的上下文:")
    for pos in hash_positions[:5]:
        start = max(0, pos - 5)
        end = min(len(encrypted_data), pos + 6)
        context = encrypted_data[start:end]
        print(f"  位置{pos}: ...{context}...")
    
    print("$ 字符周围的上下文:")
    for pos in dollar_positions[:5]:
        start = max(0, pos - 5)
        end = min(len(encrypted_data), pos + 6)
        context = encrypted_data[start:end]
        print(f"  位置{pos}: ...{context}...")
    
    # 尝试移除<>标记并解码
    print("\n=== 尝试移除<>标记 ===")
    cleaned_data = encrypted_data.replace('<', '').replace('>', '')
    print(f"移除<>后长度: {len(cleaned_data)}")
    print(f"移除<>后前100字符: {cleaned_data[:100]}")
    
    # 尝试Base64解码
    try:
        import base64
        base64_data = cleaned_data.replace('#', '=').replace('$', '/')
        while len(base64_data) % 4 != 0:
            base64_data += '='
        
        decoded_bytes = base64.b64decode(base64_data)
        decoded_text = decoded_bytes.decode('utf-8', errors='ignore')
        
        print(f"Base64解码成功，长度: {len(decoded_text)}")
        print(f"解码结果前200字符: {decoded_text[:200]}")
        
        if '<Roles' in decoded_text:
            print("✓ 解码结果包含预期的XML结构！")
            
            # 保存结果
            with open("gamedata/modcache/HYYM/saves/save0", 'r', encoding='utf-8') as f:
                original = f.read()
            header = original[:original.find('@') + 1]
            
            output_file = "gamedata/modcache/HYYM/saves/save0_pattern_decrypted.txt"
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(header + decoded_text)
            
            print(f"解密结果已保存到: {output_file}")
            return True
        
    except Exception as e:
        print(f"Base64解码失败: {e}")
    
    return False

def main():
    """主函数"""
    print("加密模式分析工具")
    print("=" * 50)
    
    success = analyze_patterns()
    
    if success:
        print("\n🎉 解密成功！")
    else:
        print("\n❌ 解密失败，需要进一步分析")

if __name__ == "__main__":
    main()
