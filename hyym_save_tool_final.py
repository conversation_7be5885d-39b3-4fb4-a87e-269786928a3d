#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HYYM存档加密解密工具 - 最终版本
基于对save0文件的分析结果
"""

import base64
import os

def decrypt_save(input_file, output_file):
    """解密存档文件"""
    print(f"🔓 开始解密: {input_file}")
    
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 分离头部和加密数据
        at_pos = content.find('@')
        if at_pos == -1:
            print("❌ 错误：文件格式不正确，未找到@符号")
            return False
        
        header = content[:at_pos + 1]
        encrypted_data = content[at_pos + 1:]
        
        # 检查是否以</gamesave>结尾
        if encrypted_data.endswith('</gamesave>'):
            encrypted_data = encrypted_data[:-12]  # 移除</gamesave>
        
        print(f"📊 文件信息:")
        print(f"   - 头部长度: {len(header)}")
        print(f"   - 加密数据长度: {len(encrypted_data)}")
        
        # 替换字符并解码
        base64_data = encrypted_data.replace('#', '=').replace('$', '/')
        
        # 确保长度是4的倍数
        while len(base64_data) % 4 != 0:
            base64_data += '='
        
        # Base64解码
        decoded_bytes = base64.b64decode(base64_data)
        
        # 尝试不同的编码
        encodings = ['utf-8', 'gbk', 'gb2312', 'latin1']
        decoded_text = None
        
        for encoding in encodings:
            try:
                decoded_text = decoded_bytes.decode(encoding)
                print(f"✅ 使用 {encoding} 编码解码成功")
                break
            except UnicodeDecodeError:
                continue
        
        if decoded_text is None:
            print("❌ 所有编码尝试都失败了")
            return False
        
        # 组合结果
        result = header + decoded_text + '</gamesave>'
        
        with open(output_file, 'w', encoding='utf-8', errors='ignore') as f:
            f.write(result)
        
        print(f"✅ 解密成功！结果保存到: {output_file}")
        print(f"📝 解密后文件长度: {len(result)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 解密失败: {e}")
        return False

def encrypt_save(input_file, output_file):
    """加密存档文件"""
    print(f"🔐 开始加密: {input_file}")
    
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 分离头部和数据
        at_pos = content.find('@')
        if at_pos == -1:
            print("❌ 错误：文件格式不正确，未找到@符号")
            return False
        
        header = content[:at_pos + 1]
        data_part = content[at_pos + 1:]
        
        # 移除</gamesave>如果存在
        if data_part.endswith('</gamesave>'):
            data_part = data_part[:-12]
        
        print(f"📊 文件信息:")
        print(f"   - 头部长度: {len(header)}")
        print(f"   - 数据部分长度: {len(data_part)}")
        
        # Base64编码
        encoded_bytes = data_part.encode('utf-8')
        base64_encoded = base64.b64encode(encoded_bytes).decode('ascii')
        
        # 替换字符以匹配游戏格式
        encrypted_data = base64_encoded.replace('=', '#').replace('/', '$')
        
        # 组合结果
        result = header + encrypted_data + '</gamesave>'
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(result)
        
        print(f"✅ 加密成功！结果保存到: {output_file}")
        print(f"📝 加密后文件长度: {len(result)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 加密失败: {e}")
        return False

def create_template_save():
    """创建模板存档文件"""
    template_content = '''<gamesave name="save" newbieTask="" information="头像.主角#慕容青璇#0001-01-31 18:00:00#风月小筑北#normal#1#初出茅庐#2">@<Roles key="主角" animation="nvfenyi" name="慕容青璇" head="头像.主角" maxhp="1420" maxmp="1400" wuxing="22" shenfa="8" bili="29" gengu="17" fuyuan="20" dingli="52" quanzhang="11" jianfa="17" daofa="26" qimen="15" currentSkillName="" exp="0" female="1" leftpoint="4" grow_template="主角" level="1" talent="女性#单身#妙手仁心#精明" jiushen_count="0">
<sk equipped="1" level="1" exp="0" name="花拳绣腿" />
<i equipped="1" level="10" exp="0" name="基本内功" />
<sp equipped="1" level="0" exp="0" name="沉鱼落雁" />
<sp equipped="1" level="0" exp="0" name="解毒" />
<sp equipped="1" level="0" exp="0" name="圣手回天" />
<e n="盘古斧" c="1" />
<e n="盘古战甲" c="1" />
<e n="盘古之心" c="1" />
<e n="盘古开天法" c="1" />
</Roles>
<i n="小还丹" c="99" />
<i n="大还丹" c="99" />
<i n="无敌宝箱" c="10" />
<k k="money" v="999999" />
<k k="date" v="0001-01-31 18:00:00" />
<k k="location.大地图" v="风月小筑北" />
<k k="mode" v="normal" />
<k k="currentNick" v="初出茅庐" />
<k k="HAOGAN善良" v="100" />
<k k="HAOGAN邪恶" v="0" />
<k k="HAOGAN魅力" v="999" />
<k k="HAOGAN容貌" v="999" />
</gamesave>'''
    
    template_file = "save_template.txt"
    with open(template_file, 'w', encoding='utf-8') as f:
        f.write(template_content)
    
    print(f"📄 模板文件已创建: {template_file}")
    print("💡 您可以编辑此模板文件来修改游戏数据：")
    print("   - money: 金钱数量")
    print("   - 小还丹/大还丹 c属性: 物品数量")
    print("   - maxhp/maxmp: 生命值/法力值")
    print("   - HAOGAN属性: 好感度数值")
    
    return template_file

def main():
    """主函数"""
    print("🎮 HYYM存档加密解密工具")
    print("=" * 50)
    print("1. 解密存档文件")
    print("2. 加密存档文件")
    print("3. 创建修改模板")
    print("4. 快速修改存档")
    
    choice = input("\n请选择操作 (1-4): ").strip()
    
    if choice == '1':
        input_file = input("请输入要解密的文件路径: ").strip()
        if not os.path.exists(input_file):
            print(f"❌ 文件不存在: {input_file}")
            return
        
        output_file = input("请输入输出文件路径 (默认: decrypted_save.txt): ").strip()
        if not output_file:
            output_file = "decrypted_save.txt"
        
        decrypt_save(input_file, output_file)
        
    elif choice == '2':
        input_file = input("请输入要加密的文件路径: ").strip()
        if not os.path.exists(input_file):
            print(f"❌ 文件不存在: {input_file}")
            return
        
        output_file = input("请输入输出文件路径 (默认: encrypted_save): ").strip()
        if not output_file:
            output_file = "encrypted_save"
        
        encrypt_save(input_file, output_file)
        
    elif choice == '3':
        template_file = create_template_save()
        print(f"\n✅ 模板创建完成！")
        print(f"📝 编辑 {template_file} 文件来修改游戏数据")
        print(f"🔐 然后使用选项2来加密文件")
        
    elif choice == '4':
        print("🚀 快速修改存档")
        input_file = input("请输入原始存档文件路径: ").strip()
        if not os.path.exists(input_file):
            print(f"❌ 文件不存在: {input_file}")
            return
        
        # 先解密
        temp_decrypted = "temp_decrypted.txt"
        if decrypt_save(input_file, temp_decrypted):
            print("\n📝 现在您可以编辑解密后的文件:")
            print(f"   文件位置: {temp_decrypted}")
            input("编辑完成后按回车继续...")
            
            # 重新加密
            output_file = input("请输入新存档文件路径: ").strip()
            if encrypt_save(temp_decrypted, output_file):
                # 清理临时文件
                os.remove(temp_decrypted)
                print("🎉 存档修改完成！")
        
    else:
        print("❌ 无效选择")

if __name__ == "__main__":
    main()
