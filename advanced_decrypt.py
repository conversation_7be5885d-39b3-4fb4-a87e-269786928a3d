#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级游戏存档解密工具
通过分析已知的明文-密文对来破解加密算法
"""

import re
import base64
from collections import Counter, defaultdict

def extract_data_parts(file_content):
    """提取文件的各个部分"""
    # 找到@符号的位置
    at_pos = file_content.find('@')
    if at_pos == -1:
        return None, None
    
    header = file_content[:at_pos + 1]
    data = file_content[at_pos + 1:]
    
    return header, data

def analyze_character_mapping():
    """分析字符映射关系"""
    # 读取加密文件
    with open("gamedata/modcache/HYYM/saves/save0", 'r', encoding='utf-8') as f:
        encrypted_content = f.read()

    # 读取解密文件
    with open("gamedata/modcache/HYYM/saves/autosave.txt", 'r', encoding='utf-8') as f:
        decrypted_content = f.read()

    # 提取数据部分
    enc_header, enc_data = extract_data_parts(encrypted_content)

    # 对于autosave.txt，它可能是完全解密的，没有@符号
    if '@' in decrypted_content:
        dec_header, dec_data = extract_data_parts(decrypted_content)
    else:
        # 整个文件就是解密后的内容，提取XML部分
        xml_start = decrypted_content.find('<Roles')
        if xml_start != -1:
            dec_data = decrypted_content[xml_start:]
        else:
            dec_data = decrypted_content

    if not enc_data:
        print("无法提取加密数据部分")
        return None

    if not dec_data:
        print("无法提取解密数据部分")
        return None
    
    print(f"加密数据长度: {len(enc_data)}")
    print(f"解密数据长度: {len(dec_data)}")
    print(f"加密数据前100字符: {enc_data[:100]}")
    print(f"解密数据前100字符: {dec_data[:100]}")
    
    # 尝试Base64解码
    print("\n=== 尝试Base64解码 ===")
    try:
        # 替换特殊字符
        base64_data = enc_data.replace('#', '=').replace('$', '/')
        
        # 确保长度是4的倍数
        while len(base64_data) % 4 != 0:
            base64_data += '='
        
        decoded_bytes = base64.b64decode(base64_data)
        print(f"Base64解码成功，得到 {len(decoded_bytes)} 字节")
        
        # 检查解码后的数据是否与目标匹配
        if decoded_bytes.decode('utf-8', errors='ignore').startswith('<Roles'):
            print("解码结果看起来正确！")
            return decoded_bytes.decode('utf-8', errors='ignore')
        else:
            print("解码结果不匹配，尝试其他方法...")
            
    except Exception as e:
        print(f"Base64解码失败: {e}")
    
    # 如果Base64失败，尝试字符替换分析
    print("\n=== 分析字符替换模式 ===")
    
    # 分析字符频率
    enc_freq = Counter(enc_data)
    dec_freq = Counter(dec_data)
    
    print("加密数据最常见字符:", enc_freq.most_common(10))
    print("解密数据最常见字符:", dec_freq.most_common(10))
    
    # 尝试建立映射关系
    # 假设这是一个简单的字符替换密码
    mapping = {}
    
    # 基于频率分析建立初步映射
    enc_sorted = [char for char, count in enc_freq.most_common()]
    dec_sorted = [char for char, count in dec_freq.most_common()]
    
    # 尝试映射最常见的字符
    for i in range(min(len(enc_sorted), len(dec_sorted))):
        mapping[enc_sorted[i]] = dec_sorted[i]
    
    print(f"建立了 {len(mapping)} 个字符映射")
    
    # 应用映射并检查结果
    result = ""
    for char in enc_data:
        if char in mapping:
            result += mapping[char]
        else:
            result += char
    
    print(f"映射结果前100字符: {result[:100]}")
    
    return result

def create_decrypted_file(decrypted_data):
    """创建解密后的文件"""
    # 读取原始加密文件的头部
    with open("gamedata/modcache/HYYM/saves/save0", 'r', encoding='utf-8') as f:
        encrypted_content = f.read()
    
    header, _ = extract_data_parts(encrypted_content)
    
    # 组合头部和解密数据
    final_content = header + decrypted_data
    
    # 保存到新文件
    output_file = "gamedata/modcache/HYYM/saves/save0_final_decrypted.txt"
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(final_content)
    
    print(f"最终解密文件已保存到: {output_file}")
    return output_file

def main():
    """主函数"""
    print("高级HYYM游戏存档解密工具")
    print("=" * 50)
    
    # 分析并解密
    decrypted_data = analyze_character_mapping()
    
    if decrypted_data:
        # 创建最终的解密文件
        output_file = create_decrypted_file(decrypted_data)
        print(f"\n解密成功！文件保存为: {output_file}")
        
        # 验证结果
        print("\n=== 验证解密结果 ===")
        with open(output_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        if '<Roles' in content and '</gamesave>' in content:
            print("✓ 解密结果包含预期的XML结构")
        else:
            print("✗ 解密结果可能不正确")
            
        # 显示前几行内容
        lines = content.split('\n')[:5]
        print("解密文件前几行:")
        for i, line in enumerate(lines, 1):
            print(f"{i}: {line[:100]}...")
    else:
        print("解密失败！")

if __name__ == "__main__":
    main()
