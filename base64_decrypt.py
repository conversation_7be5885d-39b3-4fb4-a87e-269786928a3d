#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门的Base64解密工具
"""

import base64
import re

def extract_encrypted_data(file_path):
    """提取加密数据"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 找到@符号后的内容
    at_pos = content.find('@')
    if at_pos == -1:
        return None, None
    
    header = content[:at_pos + 1]
    encrypted_data = content[at_pos + 1:]
    
    return header, encrypted_data

def try_base64_variants(data):
    """尝试不同的Base64变体"""
    variants = [
        # 原始数据
        data,
        # 替换特殊字符
        data.replace('#', '=').replace('$', '/'),
        data.replace('#', '=').replace('$', '+'),
        data.replace('#', '/').replace('$', '='),
        data.replace('#', '+').replace('$', '='),
        # 移除末尾的特殊字符
        data.rstrip('#$'),
        data.replace('#', '=').replace('$', '/').rstrip('='),
    ]
    
    for i, variant in enumerate(variants):
        print(f"\n尝试变体 {i + 1}: {variant[:50]}...")
        
        try:
            # 确保长度是4的倍数
            while len(variant) % 4 != 0:
                variant += '='
            
            decoded_bytes = base64.b64decode(variant)
            print(f"  解码成功，得到 {len(decoded_bytes)} 字节")
            
            # 尝试不同的编码
            encodings = ['utf-8', 'gbk', 'gb2312', 'latin1', 'cp1252']
            for encoding in encodings:
                try:
                    decoded_text = decoded_bytes.decode(encoding)
                    if decoded_text.startswith('<') and 'Roles' in decoded_text:
                        print(f"  使用编码 {encoding} 解码成功！")
                        return decoded_text
                    elif len(decoded_text) > 100:  # 如果解码出了合理长度的文本
                        print(f"  使用编码 {encoding} 解码出文本: {decoded_text[:100]}...")
                except UnicodeDecodeError:
                    continue
                    
        except Exception as e:
            print(f"  解码失败: {e}")
            continue
    
    return None

def main():
    """主函数"""
    print("Base64解密工具")
    print("=" * 50)
    
    # 提取加密数据
    header, encrypted_data = extract_encrypted_data("gamedata/modcache/HYYM/saves/save0")
    
    if not encrypted_data:
        print("无法提取加密数据")
        return
    
    print(f"提取到加密数据，长度: {len(encrypted_data)}")
    print(f"数据前100字符: {encrypted_data[:100]}")
    
    # 分析数据特征
    unique_chars = set(encrypted_data)
    print(f"唯一字符数: {len(unique_chars)}")
    print(f"字符集: {sorted(unique_chars)}")
    
    # 检查是否包含Base64字符
    base64_chars = set('ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=')
    special_chars = unique_chars - base64_chars
    print(f"非标准Base64字符: {special_chars}")
    
    # 尝试解密
    decrypted_text = try_base64_variants(encrypted_data)
    
    if decrypted_text:
        # 保存结果
        output_file = "gamedata/modcache/HYYM/saves/save0_base64_decrypted.txt"
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(header + decrypted_text)
        
        print(f"\n解密成功！结果保存到: {output_file}")
        
        # 显示前几行
        lines = decrypted_text.split('\n')[:5]
        print("\n解密内容前几行:")
        for i, line in enumerate(lines, 1):
            print(f"{i}: {line}")
    else:
        print("\n所有Base64变体都解密失败")

if __name__ == "__main__":
    main()
